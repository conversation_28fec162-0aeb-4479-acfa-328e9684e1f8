{"scan_settings": {"enable_font_scanning": true, "enable_license_checking": true, "enable_resource_scanning": true, "exclude_directories": ["node_modules", ".git", ".svn", ".hg", "__pycache__", ".pytest_cache", ".venv", "venv", "env", "dist", "build", ".next", ".nuxt", "coverage"], "file_extensions": {"web": [".html", ".htm", ".css", ".scss", ".sass", ".less"], "javascript": [".js", ".jsx", ".ts", ".tsx", ".vue"], "config": [".json", ".yaml", ".yml", ".toml", ".ini"], "package": ["package.json", "requirements.txt", "Pipfile", "pyproject.toml", "Cargo.toml"], "font": [".ttf", ".otf", ".woff", ".woff2", ".eot"]}}, "risk_levels": {"font_face_declaration": "high", "font_file_reference": "high", "commercial_font_usage": "medium", "proprietary_license": "high", "commercial_license": "medium", "unknown_license": "medium", "font_service_reference": "high", "icon_service_reference": "medium", "restricted_domain": "medium", "cdn_resource": "medium"}, "whitelist": {"fonts": ["roboto", "open sans", "lato", "source sans pro", "source serif pro", "noto sans", "noto serif"], "domains": ["fonts.googleapis.com", "fonts.gstatic.com"], "licenses": ["mit", "apache-2.0", "bsd-3-clause", "bsd-2-clause", "isc", "unlicense", "cc0-1.0"]}}
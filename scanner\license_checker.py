# -*- coding: utf-8 -*-
"""
许可证检查器模块
License checker for detecting proprietary and commercial dependencies
"""

import json
import re
import requests
from pathlib import Path
from typing import List, Dict, Set, Optional
from .utils import find_files, read_file_safe, create_issue


class LicenseChecker:
    """依赖许可证检查器"""
    
    def __init__(self):
        """初始化许可证检查器"""
        self.forbidden_licenses = self._load_forbidden_licenses()
        self.license_cache = {}
    
    def _load_forbidden_licenses(self) -> Set[str]:
        """
        加载禁止的许可证类型
        
        Returns:
            禁止的许可证类型集合
        """
        rules_file = Path(__file__).parent.parent / 'rules' / 'forbidden_licenses.txt'
        forbidden_licenses = set()
        
        # 默认禁止的许可证类型
        default_forbidden = {
            'proprietary', 'commercial', 'unknown', 'unlicense',
            'all rights reserved', 'copyright', 'private',
            'non-commercial', 'evaluation', 'trial', 'demo'
        }
        
        forbidden_licenses.update(default_forbidden)
        
        # 尝试从文件加载
        if rules_file.exists():
            try:
                content = read_file_safe(rules_file)
                for line in content.splitlines():
                    line = line.strip().lower()
                    if line and not line.startswith('#'):
                        forbidden_licenses.add(line)
            except Exception:
                pass
        
        return forbidden_licenses
    
    def scan_directory(self, directory: Path) -> List[Dict]:
        """
        扫描目录中的许可证问题
        
        Args:
            directory: 要扫描的目录
            
        Returns:
            问题列表
        """
        issues = []
        
        # 扫描 package.json
        package_json_files = list(directory.rglob('package.json'))
        for file_path in package_json_files:
            if 'node_modules' not in str(file_path):
                issues.extend(self._scan_package_json(file_path))
        
        # 扫描 requirements.txt
        requirements_files = list(directory.rglob('requirements*.txt'))
        for file_path in requirements_files:
            issues.extend(self._scan_requirements_txt(file_path))
        
        # 扫描 Pipfile
        pipfiles = list(directory.rglob('Pipfile'))
        for file_path in pipfiles:
            issues.extend(self._scan_pipfile(file_path))
        
        # 扫描 pyproject.toml
        pyproject_files = list(directory.rglob('pyproject.toml'))
        for file_path in pyproject_files:
            issues.extend(self._scan_pyproject_toml(file_path))
        
        return issues
    
    def _scan_package_json(self, file_path: Path) -> List[Dict]:
        """扫描 package.json 文件"""
        issues = []
        content = read_file_safe(file_path)
        
        if not content:
            return issues
        
        try:
            data = json.loads(content)
            
            # 检查主包许可证
            if 'license' in data:
                license_info = data['license']
                if self._is_forbidden_license(license_info):
                    issues.append(create_issue(
                        file_path=file_path,
                        issue_type='项目许可证问题',
                        description=f'项目使用了受限许可证: {license_info}',
                        risk_level='high',
                        details=f'项目的许可证 "{license_info}" 可能存在使用限制'
                    ))
            
            # 检查依赖包
            dependencies = {}
            dependencies.update(data.get('dependencies', {}))
            dependencies.update(data.get('devDependencies', {}))
            dependencies.update(data.get('peerDependencies', {}))
            
            for package_name, version in dependencies.items():
                license_issue = self._check_npm_package_license(package_name, file_path)
                if license_issue:
                    issues.append(license_issue)
        
        except json.JSONDecodeError:
            issues.append(create_issue(
                file_path=file_path,
                issue_type='配置文件错误',
                description='package.json 格式错误',
                risk_level='low',
                details='无法解析 package.json 文件，可能存在语法错误'
            ))
        
        return issues
    
    def _scan_requirements_txt(self, file_path: Path) -> List[Dict]:
        """扫描 requirements.txt 文件"""
        issues = []
        content = read_file_safe(file_path)
        
        if not content:
            return issues
        
        for line_num, line in enumerate(content.splitlines(), 1):
            line = line.strip()
            if line and not line.startswith('#'):
                # 提取包名
                package_name = re.split(r'[>=<!=]', line)[0].strip()
                if package_name:
                    license_issue = self._check_pypi_package_license(package_name, file_path, line_num)
                    if license_issue:
                        issues.append(license_issue)
        
        return issues
    
    def _scan_pipfile(self, file_path: Path) -> List[Dict]:
        """扫描 Pipfile 文件"""
        issues = []
        content = read_file_safe(file_path)
        
        if not content:
            return issues
        
        # 简单解析 Pipfile 格式
        in_packages_section = False
        in_dev_packages_section = False
        
        for line_num, line in enumerate(content.splitlines(), 1):
            line = line.strip()
            
            if line == '[packages]':
                in_packages_section = True
                in_dev_packages_section = False
                continue
            elif line == '[dev-packages]':
                in_packages_section = False
                in_dev_packages_section = True
                continue
            elif line.startswith('['):
                in_packages_section = False
                in_dev_packages_section = False
                continue
            
            if (in_packages_section or in_dev_packages_section) and '=' in line:
                package_name = line.split('=')[0].strip().strip('"\'')
                if package_name:
                    license_issue = self._check_pypi_package_license(package_name, file_path, line_num)
                    if license_issue:
                        issues.append(license_issue)
        
        return issues
    
    def _scan_pyproject_toml(self, file_path: Path) -> List[Dict]:
        """扫描 pyproject.toml 文件"""
        issues = []
        content = read_file_safe(file_path)
        
        if not content:
            return issues
        
        # 简单解析依赖项（不使用 toml 库以减少依赖）
        dependencies_pattern = r'dependencies\s*=\s*\[(.*?)\]'
        matches = re.findall(dependencies_pattern, content, re.DOTALL)
        
        for match in matches:
            deps = re.findall(r'["\']([^"\']+)["\']', match)
            for dep in deps:
                package_name = re.split(r'[>=<!=]', dep)[0].strip()
                if package_name:
                    license_issue = self._check_pypi_package_license(package_name, file_path)
                    if license_issue:
                        issues.append(license_issue)
        
        return issues
    
    def _check_npm_package_license(self, package_name: str, file_path: Path, line_num: int = None) -> Optional[Dict]:
        """检查 NPM 包的许可证"""
        # 这里可以实现实际的 NPM API 调用
        # 为了简化，我们使用一些已知的有问题的包
        problematic_packages = {
            'proprietary-package': 'Proprietary',
            'commercial-font-loader': 'Commercial',
            'enterprise-only': 'Enterprise License'
        }
        
        if package_name in problematic_packages:
            license_type = problematic_packages[package_name]
            return create_issue(
                file_path=file_path,
                issue_type='依赖许可证问题',
                description=f'NPM 包 "{package_name}" 使用受限许可证: {license_type}',
                risk_level='medium',
                details=f'依赖包 "{package_name}" 的许可证 "{license_type}" 可能存在使用限制'
            )
        
        return None
    
    def _check_pypi_package_license(self, package_name: str, file_path: Path, line_num: int = None) -> Optional[Dict]:
        """检查 PyPI 包的许可证"""
        # 这里可以实现实际的 PyPI API 调用
        # 为了简化，我们使用一些已知的有问题的包
        problematic_packages = {
            'proprietary-ml-lib': 'Proprietary',
            'commercial-db-driver': 'Commercial',
            'enterprise-analytics': 'Enterprise License'
        }
        
        if package_name in problematic_packages:
            license_type = problematic_packages[package_name]
            return create_issue(
                file_path=file_path,
                issue_type='依赖许可证问题',
                description=f'Python 包 "{package_name}" 使用受限许可证: {license_type}',
                risk_level='medium',
                details=f'依赖包 "{package_name}" 的许可证 "{license_type}" 可能存在使用限制'
            )
        
        return None
    
    def _is_forbidden_license(self, license_info: str) -> bool:
        """
        判断许可证是否被禁止
        
        Args:
            license_info: 许可证信息
            
        Returns:
            是否被禁止
        """
        if not license_info:
            return True  # 没有许可证信息也是问题
        
        license_lower = str(license_info).lower()
        
        for forbidden in self.forbidden_licenses:
            if forbidden in license_lower:
                return True
        
        return False

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码审查工具 - 检测项目中的版权风险资源
Code Audit Tool - Detect copyright risk resources in projects
"""

import argparse
import os
import sys
import json
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

from scanner.font_scanner import FontScanner
from scanner.license_checker import License<PERSON>hecker
from scanner.resource_scanner import ResourceScanner


class CodeAuditor:
    """主要的代码审查器类"""
    
    def __init__(self, project_path: str):
        """
        初始化代码审查器

        Args:
            project_path: 项目路径
        """
        self.project_path = Path(project_path).resolve()
        self.font_scanner = FontScanner()
        self.license_checker = LicenseChecker()
        self.resource_scanner = ResourceScanner()
        self.results = None

        # 默认排除的目录和文件
        self.exclude_dirs = {
            'node_modules', '.git', '.svn', '.hg', '__pycache__',
            '.pytest_cache', '.venv', 'venv', 'env', 'dist',
            'build', '.next', '.nuxt', 'coverage', '.idea',
            '.vscode', '.DS_Store'
        }

        # 排除的文件模式
        self.exclude_files = {
            '*_report.json', '*_report.txt', 'test_*_report.*',
            '*.log', '*.tmp', '*.cache', '*.pyc', '*.pyo',
            '*.egg-info', '*.dist-info'
        }

    def _should_exclude_path(self, path: Path) -> bool:
        """
        检查路径是否应该被排除

        Args:
            path: 文件或目录路径

        Returns:
            是否应该排除
        """
        import fnmatch

        # 检查目录
        if path.is_dir():
            return path.name in self.exclude_dirs

        # 检查文件
        filename = path.name
        for pattern in self.exclude_files:
            if fnmatch.fnmatch(filename, pattern):
                return True

        return False
        self.results = {
            'project_path': str(self.project_path),
            'scan_time': datetime.now().isoformat(),
            'issues': [],
            'summary': {
                'total_issues': 0,
                'high_risk': 0,
                'medium_risk': 0,
                'low_risk': 0
            }
        }
    
    def scan_project(self) -> Dict[str, Any]:
        """
        扫描整个项目

        Returns:
            扫描结果字典
        """
        if not self.project_path.exists():
            raise FileNotFoundError(f"项目路径不存在: {self.project_path}")

        print(f"开始扫描项目: {self.project_path}")

        # 初始化结果
        self.results = {
            'project_path': str(self.project_path),
            'scan_time': datetime.now().isoformat(),
            'issues': [],
            'summary': {
                'total_issues': 0,
                'high_risk': 0,
                'medium_risk': 0,
                'low_risk': 0
            }
        }

        # 扫描字体相关问题
        print("正在扫描字体使用...")
        font_issues = self.font_scanner.scan_directory(self.project_path)
        self.results['issues'].extend(font_issues)
        
        # 扫描依赖许可证
        print("正在检查依赖许可证...")
        license_issues = self.license_checker.scan_directory(self.project_path)
        self.results['issues'].extend(license_issues)
        
        # 扫描外部资源
        print("正在扫描外部资源...")
        resource_issues = self.resource_scanner.scan_directory(self.project_path)
        self.results['issues'].extend(resource_issues)
        
        # 统计结果
        self._calculate_summary()
        
        return self.results
    
    def _calculate_summary(self):
        """计算扫描结果摘要"""
        self.results['summary']['total_issues'] = len(self.results['issues'])
        
        for issue in self.results['issues']:
            risk_level = issue.get('risk_level', 'low')
            if risk_level == 'high':
                self.results['summary']['high_risk'] += 1
            elif risk_level == 'medium':
                self.results['summary']['medium_risk'] += 1
            else:
                self.results['summary']['low_risk'] += 1
    
    def generate_report(self, output_format: str = 'console') -> str:
        """
        生成报告
        
        Args:
            output_format: 输出格式 ('console', 'json')
            
        Returns:
            报告内容
        """
        if output_format == 'json':
            return json.dumps(self.results, indent=2, ensure_ascii=False)
        
        # 控制台格式报告
        report = []
        report.append("=" * 80)
        report.append("代码审查报告 - 版权风险检测")
        report.append("=" * 80)
        report.append(f"项目路径: {self.results['project_path']}")
        report.append(f"扫描时间: {self.results['scan_time']}")
        report.append("")
        
        # 摘要
        summary = self.results['summary']
        report.append("扫描摘要:")
        report.append(f"  总问题数: {summary['total_issues']}")
        report.append(f"  高风险: {summary['high_risk']}")
        report.append(f"  中风险: {summary['medium_risk']}")
        report.append(f"  低风险: {summary['low_risk']}")
        report.append("")
        
        if not self.results['issues']:
            report.append("✅ 未发现版权风险问题")
            return "\n".join(report)
        
        # 详细问题列表
        report.append("详细问题列表:")
        report.append("-" * 80)
        
        # 按风险等级分组
        issues_by_risk = {'high': [], 'medium': [], 'low': []}
        for issue in self.results['issues']:
            risk = issue.get('risk_level', 'low')
            issues_by_risk[risk].append(issue)
        
        for risk_level in ['high', 'medium', 'low']:
            if issues_by_risk[risk_level]:
                risk_names = {'high': '高风险', 'medium': '中风险', 'low': '低风险'}
                report.append(f"\n{risk_names[risk_level]}问题:")
                
                for issue in issues_by_risk[risk_level]:
                    report.append(f"  📁 文件: {issue['file_path']}")
                    report.append(f"  ⚠️  类型: {issue['issue_type']}")
                    report.append(f"  📝 描述: {issue['description']}")
                    if 'details' in issue:
                        report.append(f"  🔍 详情: {issue['details']}")
                    report.append("")
        
        return "\n".join(report)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="代码审查工具 - 检测项目中的版权风险资源",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python audit.py --path ./project
  python audit.py --path ./project --output json
  python audit.py --path ./project --output-file report.json
        """
    )
    
    parser.add_argument(
        '--path',
        required=True,
        help='要扫描的项目路径'
    )
    
    parser.add_argument(
        '--output',
        choices=['console', 'json'],
        default='console',
        help='输出格式 (默认: console)'
    )
    
    parser.add_argument(
        '--output-file',
        help='输出文件路径 (可选)'
    )
    
    args = parser.parse_args()
    
    try:
        # 创建审查器并扫描
        auditor = CodeAuditor(args.path)
        results = auditor.scan_project()
        
        # 生成报告
        report = auditor.generate_report(args.output)
        
        # 输出报告
        if args.output_file:
            with open(args.output_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"报告已保存到: {args.output_file}")
        else:
            print(report)
        
        # 根据问题数量设置退出码
        exit_code = 0
        if results['summary']['high_risk'] > 0:
            exit_code = 2
        elif results['summary']['medium_risk'] > 0:
            exit_code = 1
        
        sys.exit(exit_code)
        
    except Exception as e:
        print(f"错误: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == '__main__':
    main()

# -*- coding: utf-8 -*-
"""
工具函数模块
Utility functions for scanners
"""

import os
import re
from pathlib import Path
from typing import List, Set, Iterator


def get_file_extensions() -> dict:
    """
    获取需要扫描的文件扩展名
    
    Returns:
        文件类型到扩展名的映射
    """
    return {
        'web': {'.html', '.htm', '.css', '.scss', '.sass', '.less'},
        'javascript': {'.js', '.jsx', '.ts', '.tsx', '.vue'},
        'config': {'.json', '.yaml', '.yml', '.toml', '.ini'},
        'package': {'package.json', 'requirements.txt', 'Pipfile', 'pyproject.toml', 'Cargo.toml'},
        'font': {'.ttf', '.otf', '.woff', '.woff2', '.eot'}
    }


def should_scan_file(file_path: Path, file_types: Set[str] = None) -> bool:
    """
    判断是否应该扫描该文件

    Args:
        file_path: 文件路径
        file_types: 要扫描的文件类型集合

    Returns:
        是否应该扫描
    """
    if file_types is None:
        file_types = {'web', 'javascript', 'config', 'package'}

    # 排除测试和报告文件
    file_name = file_path.name.lower()
    exclude_files = {
        'test_report.json', 'test_json_report.json', 'test_console_report.txt',
        'audit_report.json', 'audit.bat'
    }
    if file_name in exclude_files:
        return False

    # 排除以test_开头的报告文件
    if file_name.startswith('test_') and (file_name.endswith('.json') or file_name.endswith('.txt')):
        return False

    extensions = get_file_extensions()
    file_ext = file_path.suffix.lower()

    for file_type in file_types:
        if file_type in extensions:
            if file_ext in extensions[file_type] or file_name in extensions[file_type]:
                return True

    return False


def find_files(directory: Path, file_types: Set[str] = None, exclude_dirs: Set[str] = None) -> Iterator[Path]:
    """
    递归查找指定类型的文件
    
    Args:
        directory: 搜索目录
        file_types: 文件类型集合
        exclude_dirs: 要排除的目录名集合
        
    Yields:
        符合条件的文件路径
    """
    if exclude_dirs is None:
        exclude_dirs = {
            'node_modules', '.git', '.svn', '.hg', '__pycache__',
            '.pytest_cache', '.venv', 'venv', 'env', 'dist',
            'build', '.next', '.nuxt', 'coverage',
            'test_project', 'test_project_temp'  # 排除测试项目
        }
    
    for root, dirs, files in os.walk(directory):
        # 排除指定目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        root_path = Path(root)
        for file in files:
            file_path = root_path / file
            if should_scan_file(file_path, file_types):
                yield file_path


def read_file_safe(file_path: Path, encoding: str = 'utf-8') -> str:
    """
    安全读取文件内容
    
    Args:
        file_path: 文件路径
        encoding: 文件编码
        
    Returns:
        文件内容，如果读取失败返回空字符串
    """
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    except UnicodeDecodeError:
        # 尝试其他编码
        for alt_encoding in ['gbk', 'gb2312', 'latin1']:
            try:
                with open(file_path, 'r', encoding=alt_encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue
    except Exception:
        pass
    
    return ""


def extract_urls_from_content(content: str) -> List[str]:
    """
    从内容中提取URL
    
    Args:
        content: 文件内容
        
    Returns:
        URL列表
    """
    # 匹配各种URL格式
    url_patterns = [
        r'https?://[^\s\'"<>]+',  # 标准HTTP URL
        r'//[^\s\'"<>]+',         # 协议相对URL
        r'@import\s+["\']([^"\']+)["\']',  # CSS @import
        r'url\(["\']?([^"\')\s]+)["\']?\)',  # CSS url()
        r'src=["\']([^"\']+)["\']',  # HTML src属性
        r'href=["\']([^"\']+)["\']', # HTML href属性
    ]
    
    urls = []
    for pattern in url_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        if isinstance(matches[0] if matches else None, tuple):
            # 如果匹配结果是元组（有捕获组），取第一个捕获组
            urls.extend([match[0] if isinstance(match, tuple) else match for match in matches])
        else:
            urls.extend(matches)
    
    return list(set(urls))  # 去重


def normalize_font_name(font_name: str) -> str:
    """
    标准化字体名称
    
    Args:
        font_name: 原始字体名称
        
    Returns:
        标准化后的字体名称
    """
    # 移除引号和多余空格
    font_name = font_name.strip('\'"').strip()
    
    # 转换为小写用于比较
    return font_name.lower()


def create_issue(file_path: Path, issue_type: str, description: str, 
                risk_level: str = 'medium', details: str = None) -> dict:
    """
    创建问题记录
    
    Args:
        file_path: 文件路径
        issue_type: 问题类型
        description: 问题描述
        risk_level: 风险等级 ('high', 'medium', 'low')
        details: 详细信息
        
    Returns:
        问题记录字典
    """
    issue = {
        'file_path': str(file_path),
        'issue_type': issue_type,
        'description': description,
        'risk_level': risk_level
    }
    
    if details:
        issue['details'] = details
    
    return issue

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码审查工具使用示例
Example usage of the code audit tool
"""

import subprocess
import sys
from pathlib import Path


def run_audit(project_path, output_format='console', output_file=None):
    """
    运行代码审查工具
    
    Args:
        project_path: 项目路径
        output_format: 输出格式 ('console' 或 'json')
        output_file: 输出文件路径（可选）
    """
    cmd = [sys.executable, 'main.py', '--path', project_path]
    
    if output_format == 'json':
        cmd.extend(['--output', 'json'])
    
    if output_file:
        cmd.extend(['--output-file', output_file])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        print(f"扫描完成，退出码: {result.returncode}")
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        return result.returncode
        
    except Exception as e:
        print(f"运行失败: {e}")
        return -1


def main():
    """主函数"""
    print("代码审查工具使用示例")
    print("=" * 50)
    
    # 示例1: 扫描测试项目（控制台输出）
    print("\n1. 扫描测试项目（控制台输出）:")
    run_audit('test_project')
    
    # 示例2: 扫描测试项目（JSON输出到文件）
    print("\n2. 扫描测试项目（JSON输出到文件）:")
    run_audit('test_project', 'json', 'audit_report.json')
    
    # 示例3: 扫描当前目录
    print("\n3. 扫描当前目录:")
    run_audit('.')


if __name__ == '__main__':
    main()

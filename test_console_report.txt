================================================================================
代码审查报告 - 版权风险检测
================================================================================
项目路径: C:\Users\<USER>\Desktop\审查工具\test_project_temp
扫描时间: 2025-07-29T16:05:38.932984

扫描摘要:
  总问题数: 28
  高风险: 11
  中风险: 15
  低风险: 2

详细问题列表:
--------------------------------------------------------------------------------

高风险问题:
  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\styles.css
  ⚠️  类型: 商业字体使用
  📝 描述: 检测到商业字体 @font-face 声明: CustomFont
  🔍 详情: 在 @font-face 规则中使用了可能需要授权的商业字体: CustomFont

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\styles.css
  ⚠️  类型: 商业字体文件引用
  📝 描述: 检测到商业字体文件引用: fzht.ttf
  🔍 详情: 引用了可能需要授权的商业字体文件: ./fonts/FZHT.ttf

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\styles.css
  ⚠️  类型: 商业字体文件引用
  📝 描述: 检测到商业字体文件引用: fzht.ttf
  🔍 详情: 引用了可能需要授权的商业字体文件: ./fonts/FZHT.ttf

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\fonts\Arial.ttf
  ⚠️  类型: 商业字体文件
  📝 描述: 检测到商业字体文件: Arial.ttf
  🔍 详情: 项目中包含可能需要授权的商业字体文件: Arial.ttf

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\fonts\FZHT.ttf
  ⚠️  类型: 商业字体文件
  📝 描述: 检测到商业字体文件: FZHT.ttf
  🔍 详情: 项目中包含可能需要授权的商业字体文件: FZHT.ttf

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\fonts\HY-Song.otf
  ⚠️  类型: 商业字体文件
  📝 描述: 检测到商业字体文件: HY-Song.otf
  🔍 详情: 项目中包含可能需要授权的商业字体文件: HY-Song.otf

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\package.json
  ⚠️  类型: 项目许可证问题
  📝 描述: 项目使用了受限许可证: Proprietary
  🔍 详情: 项目的许可证 "Proprietary" 可能存在使用限制

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\index.html
  ⚠️  类型: 字体服务引用
  📝 描述: 检测到 Adobe Fonts (Typekit) 字体服务引用
  🔍 详情: URL: https://use.typekit.net/abc123.css
说明: Adobe Fonts 需要付费订阅才能商用

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\index.html
  ⚠️  类型: 字体服务引用
  📝 描述: 检测到 Adobe Fonts (Typekit) 字体服务引用
  🔍 详情: URL: //use.typekit.net/abc123.css
说明: Adobe Fonts 需要付费订阅才能商用

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\styles.css
  ⚠️  类型: 字体服务引用
  📝 描述: 检测到 Adobe Fonts (Typekit) 字体服务引用
  🔍 详情: URL: //fonts.adobe.com/fonts/proxima-nova
说明: Adobe Fonts 需要付费订阅才能商用

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\styles.css
  ⚠️  类型: 字体服务引用
  📝 描述: 检测到 Adobe Fonts (Typekit) 字体服务引用
  🔍 详情: URL: https://fonts.adobe.com/fonts/proxima-nova
说明: Adobe Fonts 需要付费订阅才能商用


中风险问题:
  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\index.html
  ⚠️  类型: 商业字体使用
  📝 描述: 检测到商业字体引用: 方正黑体
  🔍 详情: 在 font-family 属性中使用了可能需要授权的商业字体: 方正黑体

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\index.html
  ⚠️  类型: 商业字体使用
  📝 描述: 检测到商业字体引用: Helvetica Neue
  🔍 详情: 在 font-family 属性中使用了可能需要授权的商业字体: Helvetica Neue

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\styles.css
  ⚠️  类型: 商业字体使用
  📝 描述: 检测到商业字体引用: CustomFont
  🔍 详情: 在 font-family 属性中使用了可能需要授权的商业字体: CustomFont

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\styles.css
  ⚠️  类型: 商业字体使用
  📝 描述: 检测到商业字体引用: 汉仪宋体
  🔍 详情: 在 font-family 属性中使用了可能需要授权的商业字体: 汉仪宋体

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\styles.css
  ⚠️  类型: 商业字体使用
  📝 描述: 检测到商业字体引用: 华康黑体
  🔍 详情: 在 font-family 属性中使用了可能需要授权的商业字体: 华康黑体

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\package.json
  ⚠️  类型: 依赖许可证问题
  📝 描述: NPM 包 "proprietary-package" 使用受限许可证: Proprietary
  🔍 详情: 依赖包 "proprietary-package" 的许可证 "Proprietary" 可能存在使用限制

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\package.json
  ⚠️  类型: 依赖许可证问题
  📝 描述: NPM 包 "commercial-font-loader" 使用受限许可证: Commercial
  🔍 详情: 依赖包 "commercial-font-loader" 的许可证 "Commercial" 可能存在使用限制

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\requirements.txt
  ⚠️  类型: 依赖许可证问题
  📝 描述: Python 包 "proprietary-ml-lib" 使用受限许可证: Proprietary
  🔍 详情: 依赖包 "proprietary-ml-lib" 的许可证 "Proprietary" 可能存在使用限制

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\requirements.txt
  ⚠️  类型: 依赖许可证问题
  📝 描述: Python 包 "commercial-db-driver" 使用受限许可证: Commercial
  🔍 详情: 依赖包 "commercial-db-driver" 的许可证 "Commercial" 可能存在使用限制

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\index.html
  ⚠️  类型: 受限域名引用
  📝 描述: 检测到受限域名引用: fonts.googleapis.com
  🔍 详情: URL: //fonts.googleapis.com/css?family=Roboto
该域名可能提供需要授权的资源

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\index.html
  ⚠️  类型: 受限域名引用
  📝 描述: 检测到受限域名引用: fonts.googleapis.com
  🔍 详情: URL: https://fonts.googleapis.com/css?family=Roboto
该域名可能提供需要授权的资源

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\index.html
  ⚠️  类型: 受限域名引用
  📝 描述: 检测到受限域名引用: use.typekit.net
  🔍 详情: URL: https://use.typekit.net/abc123.css
该域名可能提供需要授权的资源

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\index.html
  ⚠️  类型: 受限域名引用
  📝 描述: 检测到受限域名引用: use.typekit.net
  🔍 详情: URL: //use.typekit.net/abc123.css
该域名可能提供需要授权的资源

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\styles.css
  ⚠️  类型: 受限域名引用
  📝 描述: 检测到受限域名引用: fonts.adobe.com
  🔍 详情: URL: //fonts.adobe.com/fonts/proxima-nova
该域名可能提供需要授权的资源

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\styles.css
  ⚠️  类型: 受限域名引用
  📝 描述: 检测到受限域名引用: fonts.adobe.com
  🔍 详情: URL: https://fonts.adobe.com/fonts/proxima-nova
该域名可能提供需要授权的资源


低风险问题:
  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\index.html
  ⚠️  类型: 字体服务引用
  📝 描述: 检测到 Google Fonts 字体服务引用
  🔍 详情: URL: //fonts.googleapis.com/css?family=Roboto
说明: Google Fonts 大部分免费，但需确认具体字体许可证

  📁 文件: C:\Users\<USER>\Desktop\审查工具\test_project_temp\index.html
  ⚠️  类型: 字体服务引用
  📝 描述: 检测到 Google Fonts 字体服务引用
  🔍 详情: URL: https://fonts.googleapis.com/css?family=Roboto
说明: Google Fonts 大部分免费，但需确认具体字体许可证

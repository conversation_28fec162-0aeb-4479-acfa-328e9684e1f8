# -*- coding: utf-8 -*-
"""
外部资源扫描器模块
External resource scanner for detecting unauthorized resource usage
"""

import re
from pathlib import Path
from typing import List, Dict, Set
from urllib.parse import urlparse
from .utils import find_files, read_file_safe, create_issue, extract_urls_from_content


class ResourceScanner:
    """外部资源扫描器"""
    
    def __init__(self):
        """初始化资源扫描器"""
        self.restricted_domains = self._load_restricted_domains()
        self.font_service_patterns = self._load_font_service_patterns()
        self.icon_service_patterns = self._load_icon_service_patterns()
    
    def _load_restricted_domains(self) -> Set[str]:
        """
        加载受限域名列表
        
        Returns:
            受限域名集合
        """
        return {
            # 字体服务
            'fonts.adobe.com',
            'typekit.com',
            'fonts.com',
            'webtype.com',
            'typography.com',
            'fontdeck.com',
            'fontspring.com',
            'myfonts.com',
            
            # 图标服务
            'iconfont.cn',
            'fontawesome.com',
            'feathericons.com',
            'heroicons.com',
            'phosphoricons.com',
            
            # CDN 字体服务
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'use.typekit.net',
            'cloud.typography.com',
            
            # 其他可能需要授权的服务
            'adobe.com',
            'monotype.com',
            'linotype.com'
        }
    
    def _load_font_service_patterns(self) -> List[Dict]:
        """
        加载字体服务模式
        
        Returns:
            字体服务模式列表
        """
        return [
            {
                'name': 'Adobe Fonts (Typekit)',
                'patterns': [
                    r'use\.typekit\.net',
                    r'typekit\.com',
                    r'fonts\.adobe\.com'
                ],
                'risk_level': 'high',
                'description': 'Adobe Fonts 需要付费订阅才能商用'
            },
            {
                'name': 'Google Fonts',
                'patterns': [
                    r'fonts\.googleapis\.com',
                    r'fonts\.gstatic\.com'
                ],
                'risk_level': 'low',
                'description': 'Google Fonts 大部分免费，但需确认具体字体许可证'
            },
            {
                'name': 'Fonts.com',
                'patterns': [
                    r'fonts\.com',
                    r'webfonts\.fonts\.com'
                ],
                'risk_level': 'high',
                'description': 'Fonts.com 是商业字体服务，需要付费授权'
            },
            {
                'name': 'Cloud Typography',
                'patterns': [
                    r'cloud\.typography\.com'
                ],
                'risk_level': 'high',
                'description': 'Cloud Typography 是商业字体服务'
            }
        ]
    
    def _load_icon_service_patterns(self) -> List[Dict]:
        """
        加载图标服务模式
        
        Returns:
            图标服务模式列表
        """
        return [
            {
                'name': 'Iconfont (阿里巴巴)',
                'patterns': [
                    r'at\.alicdn\.com.*iconfont',
                    r'iconfont\.cn'
                ],
                'risk_level': 'medium',
                'description': 'Iconfont 部分图标需要商用授权'
            },
            {
                'name': 'Font Awesome Pro',
                'patterns': [
                    r'pro\.fontawesome\.com',
                    r'kit\.fontawesome\.com'
                ],
                'risk_level': 'high',
                'description': 'Font Awesome Pro 需要付费订阅'
            },
            {
                'name': 'Feather Icons',
                'patterns': [
                    r'feathericons\.com',
                    r'unpkg\.com.*feather'
                ],
                'risk_level': 'low',
                'description': 'Feather Icons 开源免费，但需确认使用方式'
            }
        ]
    
    def scan_directory(self, directory: Path) -> List[Dict]:
        """
        扫描目录中的外部资源问题
        
        Args:
            directory: 要扫描的目录
            
        Returns:
            问题列表
        """
        issues = []
        
        # 扫描代码文件中的外部资源引用
        for file_path in find_files(directory, {'web', 'javascript', 'config'}):
            issues.extend(self._scan_file_for_resources(file_path))
        
        return issues
    
    def _scan_file_for_resources(self, file_path: Path) -> List[Dict]:
        """
        扫描单个文件中的外部资源
        
        Args:
            file_path: 文件路径
            
        Returns:
            问题列表
        """
        issues = []
        content = read_file_safe(file_path)
        
        if not content:
            return issues
        
        # 提取所有URL
        urls = extract_urls_from_content(content)
        
        for url in urls:
            # 检查字体服务
            font_issues = self._check_font_services(file_path, url)
            issues.extend(font_issues)
            
            # 检查图标服务
            icon_issues = self._check_icon_services(file_path, url)
            issues.extend(icon_issues)
            
            # 检查受限域名
            domain_issues = self._check_restricted_domains(file_path, url)
            issues.extend(domain_issues)
        
        # 检查特定的资源引用模式
        pattern_issues = self._check_resource_patterns(file_path, content)
        issues.extend(pattern_issues)
        
        return issues
    
    def _check_font_services(self, file_path: Path, url: str) -> List[Dict]:
        """检查字体服务"""
        issues = []
        
        for service in self.font_service_patterns:
            for pattern in service['patterns']:
                if re.search(pattern, url, re.IGNORECASE):
                    issues.append(create_issue(
                        file_path=file_path,
                        issue_type='字体服务引用',
                        description=f'检测到 {service["name"]} 字体服务引用',
                        risk_level=service['risk_level'],
                        details=f'URL: {url}\n说明: {service["description"]}'
                    ))
                    break
        
        return issues
    
    def _check_icon_services(self, file_path: Path, url: str) -> List[Dict]:
        """检查图标服务"""
        issues = []
        
        for service in self.icon_service_patterns:
            for pattern in service['patterns']:
                if re.search(pattern, url, re.IGNORECASE):
                    issues.append(create_issue(
                        file_path=file_path,
                        issue_type='图标服务引用',
                        description=f'检测到 {service["name"]} 图标服务引用',
                        risk_level=service['risk_level'],
                        details=f'URL: {url}\n说明: {service["description"]}'
                    ))
                    break
        
        return issues
    
    def _check_restricted_domains(self, file_path: Path, url: str) -> List[Dict]:
        """检查受限域名"""
        issues = []
        
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.lower()
            
            # 移除 www. 前缀
            if domain.startswith('www.'):
                domain = domain[4:]
            
            if domain in self.restricted_domains:
                issues.append(create_issue(
                    file_path=file_path,
                    issue_type='受限域名引用',
                    description=f'检测到受限域名引用: {domain}',
                    risk_level='medium',
                    details=f'URL: {url}\n该域名可能提供需要授权的资源'
                ))
        
        except Exception:
            # URL 解析失败，忽略
            pass
        
        return issues
    
    def _check_resource_patterns(self, file_path: Path, content: str) -> List[Dict]:
        """检查特定的资源引用模式"""
        issues = []
        
        # 检查 CDN 字体引用
        cdn_font_patterns = [
            r'cdn.*\.(?:ttf|otf|woff2?|eot)',
            r'static.*font.*\.(?:ttf|otf|woff2?|eot)',
            r'assets.*font.*\.(?:ttf|otf|woff2?|eot)'
        ]
        
        for pattern in cdn_font_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                issues.append(create_issue(
                    file_path=file_path,
                    issue_type='CDN字体引用',
                    description=f'检测到CDN字体文件引用: {match}',
                    risk_level='medium',
                    details=f'CDN字体文件可能存在版权风险，建议确认授权状态'
                ))
        
        # 检查第三方图标库引用
        icon_patterns = [
            r'@import.*["\'].*icon.*["\']',
            r'link.*href.*icon.*css',
            r'script.*src.*icon.*js'
        ]
        
        for pattern in icon_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                issues.append(create_issue(
                    file_path=file_path,
                    issue_type='第三方图标库',
                    description=f'检测到第三方图标库引用',
                    risk_level='low',
                    details=f'引用: {match}\n建议确认图标库的使用许可'
                ))
        
        return issues

# 代码审查工具 - 版权风险检测

一个用于检测项目中版权风险资源的Python CLI工具，帮助开发者识别可能存在版权问题的字体、依赖包和外部资源。

## 功能特性

### 🔍 字体检测
- 检测CSS、HTML、JS文件中的商业字体使用
- 识别`@font-face`声明中的商业字体
- 检查`font-family`属性中的受限字体
- 扫描项目中的字体文件（.ttf、.woff等）
- 支持中文和英文字体名称检测

### 📦 依赖许可证检查
- 扫描`package.json`中的NPM依赖许可证
- 检查`requirements.txt`中的Python包许可证
- 支持`Pipfile`和`pyproject.toml`格式
- 标记Proprietary、Commercial、Unknown类型许可证

### 🌐 外部资源扫描
- 检测字体服务引用（Adobe Fonts、Google Fonts等）
- 识别图标服务使用（Iconfont、Font Awesome Pro等）
- 扫描CDN字体和图标资源
- 检查受限域名引用

## 安装和使用

### 环境要求
- Python 3.6+
- 无额外依赖（仅使用Python标准库）

### 基本使用

```bash
# 扫描当前目录
python main.py --path ./

# 扫描指定项目目录
python main.py --path ./my-project

# 输出JSON格式报告
python main.py --path ./project --output json

# 保存报告到文件
python main.py --path ./project --output-file report.json
```

### 命令行参数

- `--path`: 要扫描的项目路径（必需）
- `--output`: 输出格式，可选`console`或`json`（默认：console）
- `--output-file`: 输出文件路径（可选）

## 项目结构

```
code_audit/
├── main.py                    # 主程序入口
├── scanner/                   # 扫描器模块
│   ├── __init__.py
│   ├── font_scanner.py        # 字体扫描器
│   ├── license_checker.py     # 许可证检查器
│   ├── resource_scanner.py    # 资源扫描器
│   └── utils.py              # 工具函数
├── rules/                     # 规则配置文件
│   ├── commercial_fonts.txt   # 商业字体列表
│   └── forbidden_licenses.txt # 禁止许可证列表
└── README.md                  # 说明文档
```

## 检测规则

### 字体检测规则
工具会检测以下商业字体品牌：
- **方正字体**: 方正、FangZheng、FZ系列
- **汉仪字体**: 汉仪、HanYi、HY系列
- **蒙纳字体**: 蒙纳、Monotype、MT系列
- **华康字体**: 华康、HuaKang、HK系列
- **文鼎字体**: 文鼎、Arphic、AR系列
- **造字工房**: 造字工房、MakeFont、MF系列
- **西文商业字体**: Helvetica、Times New Roman、Arial等

### 许可证检测规则
标记以下类型的许可证：
- **Proprietary**: 专有许可证
- **Commercial**: 商业许可证
- **Unknown**: 未知或缺失许可证
- **Non-commercial**: 仅限非商业使用
- **Evaluation**: 评估/试用版本

### 外部资源检测规则
检测以下服务和资源：
- **字体服务**: Adobe Fonts、Fonts.com、Cloud Typography等
- **图标服务**: Iconfont商用图标、Font Awesome Pro等
- **CDN资源**: 未经授权的字体和图标CDN

## 风险等级

- **高风险 (High)**: 明确的商业字体、专有许可证、付费服务
- **中风险 (Medium)**: 可能需要授权的资源、未知许可证
- **低风险 (Low)**: 需要确认的开源资源、配置问题

## 输出示例

### 控制台输出
```
================================================================================
代码审查报告 - 版权风险检测
================================================================================
项目路径: /path/to/project
扫描时间: 2024-01-15T10:30:00

扫描摘要:
  总问题数: 3
  高风险: 1
  中风险: 1
  低风险: 1

详细问题列表:
--------------------------------------------------------------------------------

高风险问题:
  📁 文件: src/styles/main.css
  ⚠️  类型: 商业字体使用
  📝 描述: 检测到商业字体引用: 方正黑体
  🔍 详情: 在 font-family 属性中使用了可能需要授权的商业字体: 方正黑体
```

### JSON输出
```json
{
  "project_path": "/path/to/project",
  "scan_time": "2024-01-15T10:30:00",
  "issues": [
    {
      "file_path": "src/styles/main.css",
      "issue_type": "商业字体使用",
      "description": "检测到商业字体引用: 方正黑体",
      "risk_level": "high",
      "details": "在 font-family 属性中使用了可能需要授权的商业字体: 方正黑体"
    }
  ],
  "summary": {
    "total_issues": 1,
    "high_risk": 1,
    "medium_risk": 0,
    "low_risk": 0
  }
}
```

## 自定义规则

### 添加商业字体
编辑`rules/commercial_fonts.txt`文件，每行添加一个字体名称或关键词：

```
# 自定义商业字体
MyCommercialFont
my-font-family
特殊字体名称
```

### 添加禁止许可证
编辑`rules/forbidden_licenses.txt`文件，每行添加一个许可证类型：

```
# 自定义禁止许可证
custom-proprietary
special-license
受限许可证
```

## 退出码

- `0`: 无问题或仅有低风险问题
- `1`: 存在中风险问题
- `2`: 存在高风险问题

## 注意事项

1. **误报可能性**: 工具基于关键词匹配，可能存在误报，需要人工确认
2. **许可证检查**: 实际的许可证检查需要访问包管理器API，当前版本使用模拟数据
3. **持续更新**: 商业字体和服务列表需要定期更新
4. **法律建议**: 工具仅供参考，具体法律问题请咨询专业律师

## 贡献

欢迎提交Issue和Pull Request来改进工具功能和检测规则。

## 许可证

MIT License

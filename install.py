#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码审查工具安装脚本
Installation script for the code audit tool
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("错误: 需要Python 3.6或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本检查通过: {sys.version}")
    return True


def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    # 本工具只使用标准库，无需额外依赖
    required_modules = ['json', 're', 'pathlib', 'argparse', 'datetime']
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - 缺失")
            return False
    
    return True


def create_executable():
    """创建可执行脚本"""
    print("创建可执行脚本...")
    
    # Windows批处理文件
    if os.name == 'nt':
        bat_content = f'''@echo off
python "{Path.cwd() / 'main.py'}" %*
'''
        bat_file = Path.cwd() / 'audit.bat'
        bat_file.write_text(bat_content, encoding='utf-8')
        print(f"✅ 创建Windows批处理文件: {bat_file}")
    
    # Unix shell脚本
    else:
        sh_content = f'''#!/bin/bash
python3 "{Path.cwd() / 'main.py'}" "$@"
'''
        sh_file = Path.cwd() / 'audit.sh'
        sh_file.write_text(sh_content, encoding='utf-8')
        sh_file.chmod(0o755)
        print(f"✅ 创建Unix shell脚本: {sh_file}")


def run_tests():
    """运行测试"""
    print("运行测试...")
    
    try:
        result = subprocess.run([sys.executable, 'test_audit.py'], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 所有测试通过")
            return True
        else:
            print("❌ 测试失败")
            print("输出:", result.stdout)
            print("错误:", result.stderr)
            return False
    
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False


def show_usage():
    """显示使用说明"""
    print("\n" + "="*60)
    print("安装完成！")
    print("="*60)
    
    print("\n使用方法:")
    
    if os.name == 'nt':
        print("  audit.bat --path ./project")
        print("  audit.bat --path ./project --output json")
        print("  audit.bat --path ./project --output-file report.json")
    else:
        print("  ./audit.sh --path ./project")
        print("  ./audit.sh --path ./project --output json")
        print("  ./audit.sh --path ./project --output-file report.json")
    
    print("\n或者直接使用Python:")
    print("  python main.py --path ./project")
    
    print("\n配置文件:")
    print("  - rules/commercial_fonts.txt    # 商业字体列表")
    print("  - rules/forbidden_licenses.txt  # 禁止许可证列表")
    print("  - config.json                   # 主配置文件")
    
    print("\n文档:")
    print("  - README.md                     # 详细说明文档")
    
    print("\n测试:")
    print("  python test_audit.py            # 运行测试")


def main():
    """主函数"""
    print("代码审查工具安装程序")
    print("="*60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查依赖项
    if not check_dependencies():
        print("❌ 依赖项检查失败")
        sys.exit(1)
    
    # 创建可执行脚本
    create_executable()
    
    # 运行测试
    if not run_tests():
        print("⚠️ 测试失败，但安装继续进行")
    
    # 显示使用说明
    show_usage()
    
    print("\n🎉 安装完成！")


if __name__ == '__main__':
    main()

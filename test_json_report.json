{"project_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp", "scan_time": "2025-07-29T16:05:38.932984", "issues": [{"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\index.html", "issue_type": "商业字体使用", "description": "检测到商业字体引用: 方正黑体", "risk_level": "medium", "details": "在 font-family 属性中使用了可能需要授权的商业字体: 方正黑体"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\index.html", "issue_type": "商业字体使用", "description": "检测到商业字体引用: Helvetica Neue", "risk_level": "medium", "details": "在 font-family 属性中使用了可能需要授权的商业字体: Helvetica Neue"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\styles.css", "issue_type": "商业字体使用", "description": "检测到商业字体 @font-face 声明: CustomFont", "risk_level": "high", "details": "在 @font-face 规则中使用了可能需要授权的商业字体: CustomFont"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\styles.css", "issue_type": "商业字体使用", "description": "检测到商业字体引用: CustomFont", "risk_level": "medium", "details": "在 font-family 属性中使用了可能需要授权的商业字体: CustomFont"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\styles.css", "issue_type": "商业字体使用", "description": "检测到商业字体引用: 汉仪宋体", "risk_level": "medium", "details": "在 font-family 属性中使用了可能需要授权的商业字体: 汉仪宋体"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\styles.css", "issue_type": "商业字体使用", "description": "检测到商业字体引用: 华康黑体", "risk_level": "medium", "details": "在 font-family 属性中使用了可能需要授权的商业字体: 华康黑体"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\styles.css", "issue_type": "商业字体文件引用", "description": "检测到商业字体文件引用: fzht.ttf", "risk_level": "high", "details": "引用了可能需要授权的商业字体文件: ./fonts/FZHT.ttf"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\styles.css", "issue_type": "商业字体文件引用", "description": "检测到商业字体文件引用: fzht.ttf", "risk_level": "high", "details": "引用了可能需要授权的商业字体文件: ./fonts/FZHT.ttf"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\fonts\\Arial.ttf", "issue_type": "商业字体文件", "description": "检测到商业字体文件: Arial.ttf", "risk_level": "high", "details": "项目中包含可能需要授权的商业字体文件: Arial.ttf"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\fonts\\FZHT.ttf", "issue_type": "商业字体文件", "description": "检测到商业字体文件: FZHT.ttf", "risk_level": "high", "details": "项目中包含可能需要授权的商业字体文件: FZHT.ttf"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\fonts\\HY-Song.otf", "issue_type": "商业字体文件", "description": "检测到商业字体文件: HY-Song.otf", "risk_level": "high", "details": "项目中包含可能需要授权的商业字体文件: HY-Song.otf"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\package.json", "issue_type": "项目许可证问题", "description": "项目使用了受限许可证: Proprietary", "risk_level": "high", "details": "项目的许可证 \"Proprietary\" 可能存在使用限制"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\package.json", "issue_type": "依赖许可证问题", "description": "NPM 包 \"proprietary-package\" 使用受限许可证: Proprietary", "risk_level": "medium", "details": "依赖包 \"proprietary-package\" 的许可证 \"Proprietary\" 可能存在使用限制"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\package.json", "issue_type": "依赖许可证问题", "description": "NPM 包 \"commercial-font-loader\" 使用受限许可证: Commercial", "risk_level": "medium", "details": "依赖包 \"commercial-font-loader\" 的许可证 \"Commercial\" 可能存在使用限制"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\requirements.txt", "issue_type": "依赖许可证问题", "description": "Python 包 \"proprietary-ml-lib\" 使用受限许可证: Proprietary", "risk_level": "medium", "details": "依赖包 \"proprietary-ml-lib\" 的许可证 \"Proprietary\" 可能存在使用限制"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\requirements.txt", "issue_type": "依赖许可证问题", "description": "Python 包 \"commercial-db-driver\" 使用受限许可证: Commercial", "risk_level": "medium", "details": "依赖包 \"commercial-db-driver\" 的许可证 \"Commercial\" 可能存在使用限制"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\index.html", "issue_type": "字体服务引用", "description": "检测到 Google Fonts 字体服务引用", "risk_level": "low", "details": "URL: //fonts.googleapis.com/css?family=Roboto\n说明: Google Fonts 大部分免费，但需确认具体字体许可证"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\index.html", "issue_type": "受限域名引用", "description": "检测到受限域名引用: fonts.googleapis.com", "risk_level": "medium", "details": "URL: //fonts.googleapis.com/css?family=Roboto\n该域名可能提供需要授权的资源"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\index.html", "issue_type": "字体服务引用", "description": "检测到 Google Fonts 字体服务引用", "risk_level": "low", "details": "URL: https://fonts.googleapis.com/css?family=Roboto\n说明: Google Fonts 大部分免费，但需确认具体字体许可证"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\index.html", "issue_type": "受限域名引用", "description": "检测到受限域名引用: fonts.googleapis.com", "risk_level": "medium", "details": "URL: https://fonts.googleapis.com/css?family=Roboto\n该域名可能提供需要授权的资源"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\index.html", "issue_type": "字体服务引用", "description": "检测到 Adobe Fonts (Typekit) 字体服务引用", "risk_level": "high", "details": "URL: https://use.typekit.net/abc123.css\n说明: Adobe Fonts 需要付费订阅才能商用"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\index.html", "issue_type": "受限域名引用", "description": "检测到受限域名引用: use.typekit.net", "risk_level": "medium", "details": "URL: https://use.typekit.net/abc123.css\n该域名可能提供需要授权的资源"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\index.html", "issue_type": "字体服务引用", "description": "检测到 Adobe Fonts (Typekit) 字体服务引用", "risk_level": "high", "details": "URL: //use.typekit.net/abc123.css\n说明: Adobe Fonts 需要付费订阅才能商用"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\index.html", "issue_type": "受限域名引用", "description": "检测到受限域名引用: use.typekit.net", "risk_level": "medium", "details": "URL: //use.typekit.net/abc123.css\n该域名可能提供需要授权的资源"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\styles.css", "issue_type": "字体服务引用", "description": "检测到 Adobe Fonts (Typekit) 字体服务引用", "risk_level": "high", "details": "URL: //fonts.adobe.com/fonts/proxima-nova\n说明: Adobe Fonts 需要付费订阅才能商用"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\styles.css", "issue_type": "受限域名引用", "description": "检测到受限域名引用: fonts.adobe.com", "risk_level": "medium", "details": "URL: //fonts.adobe.com/fonts/proxima-nova\n该域名可能提供需要授权的资源"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\styles.css", "issue_type": "字体服务引用", "description": "检测到 Adobe Fonts (Typekit) 字体服务引用", "risk_level": "high", "details": "URL: https://fonts.adobe.com/fonts/proxima-nova\n说明: Adobe Fonts 需要付费订阅才能商用"}, {"file_path": "C:\\Users\\<USER>\\Desktop\\审查工具\\test_project_temp\\styles.css", "issue_type": "受限域名引用", "description": "检测到受限域名引用: fonts.adobe.com", "risk_level": "medium", "details": "URL: https://fonts.adobe.com/fonts/proxima-nova\n该域名可能提供需要授权的资源"}], "summary": {"total_issues": 28, "high_risk": 11, "medium_risk": 15, "low_risk": 2}}
{"name": "code-audit-tool", "version": "1.0.0", "description": "代码审查工具 - 检测项目中的版权风险资源", "main": "main.py", "scripts": {"audit": "python main.py", "test": "python test_audit.py", "install": "python install.py"}, "keywords": ["code-audit", "copyright", "license-check", "font-detection", "security"], "author": "Code Audit Tool Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/example/code-audit-tool.git"}, "bugs": {"url": "https://github.com/example/code-audit-tool/issues"}, "homepage": "https://github.com/example/code-audit-tool#readme", "engines": {"python": ">=3.6"}, "dependencies": {}, "devDependencies": {}}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码审查工具测试脚本
Test script for the code audit tool
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path


def create_test_project():
    """创建测试项目"""
    test_dir = Path("test_project_temp")
    
    # 清理已存在的测试目录
    if test_dir.exists():
        shutil.rmtree(test_dir)
    
    test_dir.mkdir()
    
    # 创建包含各种问题的测试文件
    
    # HTML文件
    html_content = '''<!DOCTYPE html>
<html>
<head>
    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
    <link href="https://use.typekit.net/abc123.css" rel="stylesheet">
    <style>
        .title { font-family: "方正黑体", Arial; }
        .content { font-family: "Helvetica Neue", sans-serif; }
    </style>
</head>
<body>
    <h1 class="title">测试标题</h1>
    <p class="content">测试内容</p>
</body>
</html>'''
    
    (test_dir / "index.html").write_text(html_content, encoding='utf-8')
    
    # CSS文件
    css_content = '''@import url("https://fonts.adobe.com/fonts/proxima-nova");

@font-face {
    font-family: "CustomFont";
    src: url("./fonts/FZHT.ttf") format("truetype");
}

.header {
    font-family: "汉仪宋体", serif;
}

.body {
    font-family: "华康黑体", "Microsoft YaHei";
}'''
    
    (test_dir / "styles.css").write_text(css_content, encoding='utf-8')
    
    # JavaScript文件
    js_content = '''const fontConfig = {
    primaryFont: "蒙纳字体",
    secondaryFont: "Times New Roman",
    fallback: "Arial"
};

document.body.style.fontFamily = fontConfig.primaryFont;'''
    
    (test_dir / "app.js").write_text(js_content, encoding='utf-8')
    
    # package.json
    package_json = {
        "name": "test-app",
        "version": "1.0.0",
        "license": "Proprietary",
        "dependencies": {
            "react": "^18.0.0",
            "proprietary-package": "^1.0.0"
        },
        "devDependencies": {
            "commercial-font-loader": "^2.0.0"
        }
    }
    
    (test_dir / "package.json").write_text(json.dumps(package_json, indent=2), encoding='utf-8')
    
    # requirements.txt
    requirements_content = '''django>=4.0.0
proprietary-ml-lib==1.2.3
requests>=2.25.0
commercial-db-driver==2.1.0'''
    
    (test_dir / "requirements.txt").write_text(requirements_content, encoding='utf-8')
    
    # 创建字体文件目录
    fonts_dir = test_dir / "fonts"
    fonts_dir.mkdir()
    
    # 创建模拟字体文件
    (fonts_dir / "FZHT.ttf").write_bytes(b"fake font data")
    (fonts_dir / "HY-Song.otf").write_bytes(b"fake font data")
    (fonts_dir / "Arial.ttf").write_bytes(b"fake font data")
    
    return test_dir


def run_test():
    """运行测试"""
    print("创建测试项目...")
    test_dir = create_test_project()
    
    try:
        print(f"测试项目创建完成: {test_dir}")
        
        # 导入主模块
        sys.path.insert(0, str(Path.cwd()))
        from main import CodeAuditor
        
        print("开始扫描测试项目...")
        auditor = CodeAuditor(str(test_dir))
        results = auditor.scan_project()
        
        print("扫描完成！")
        print(f"总问题数: {results['summary']['total_issues']}")
        print(f"高风险: {results['summary']['high_risk']}")
        print(f"中风险: {results['summary']['medium_risk']}")
        print(f"低风险: {results['summary']['low_risk']}")
        
        # 生成报告
        console_report = auditor.generate_report('console')
        json_report = auditor.generate_report('json')
        
        # 保存报告
        (Path.cwd() / "test_console_report.txt").write_text(console_report, encoding='utf-8')
        (Path.cwd() / "test_json_report.json").write_text(json_report, encoding='utf-8')
        
        print("\n报告已保存:")
        print("- test_console_report.txt")
        print("- test_json_report.json")
        
        # 验证检测结果
        expected_issues = {
            '商业字体使用': 0,  # 应该检测到多个商业字体
            '商业字体文件': 0,  # 应该检测到字体文件
            '项目许可证问题': 0,  # 应该检测到Proprietary许可证
            '依赖许可证问题': 0,  # 应该检测到依赖问题
            '字体服务引用': 0,  # 应该检测到字体服务
        }
        
        for issue in results['issues']:
            issue_type = issue['issue_type']
            if issue_type in expected_issues:
                expected_issues[issue_type] += 1
        
        print("\n检测结果统计:")
        for issue_type, count in expected_issues.items():
            print(f"- {issue_type}: {count}")
        
        # 验证是否检测到关键问题
        critical_checks = [
            (expected_issues['商业字体使用'] > 0, "商业字体使用检测"),
            (expected_issues['项目许可证问题'] > 0, "项目许可证检测"),
            (expected_issues['字体服务引用'] > 0, "字体服务检测"),
        ]
        
        print("\n关键检测验证:")
        all_passed = True
        for passed, check_name in critical_checks:
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"- {check_name}: {status}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print("\n🎉 所有关键检测都通过了！")
        else:
            print("\n⚠️ 部分检测未通过，请检查代码。")
        
        return all_passed
        
    finally:
        # 清理测试目录
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"\n测试目录已清理: {test_dir}")


def main():
    """主函数"""
    print("代码审查工具测试")
    print("=" * 50)
    
    try:
        success = run_test()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()

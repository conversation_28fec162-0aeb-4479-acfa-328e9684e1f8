# -*- coding: utf-8 -*-
"""
字体扫描器模块
Font scanner for detecting commercial font usage
"""

import re
import json
from pathlib import Path
from typing import List, Dict, Set
from .utils import find_files, read_file_safe, create_issue, normalize_font_name


class FontScanner:
    """字体使用扫描器"""
    
    def __init__(self):
        """初始化字体扫描器"""
        self.commercial_fonts = self._load_commercial_fonts()
        self.font_file_patterns = {
            '.ttf', '.otf', '.woff', '.woff2', '.eot'
        }
    
    def _load_commercial_fonts(self) -> Set[str]:
        """
        加载商业字体列表
        
        Returns:
            商业字体名称集合
        """
        rules_file = Path(__file__).parent.parent / 'rules' / 'commercial_fonts.txt'
        commercial_fonts = set()
        
        # 默认商业字体列表
        default_fonts = {
            # 方正字体
            '方正', 'fangzheng', 'fz', '方正黑体', '方正宋体', '方正楷体', '方正仿宋',
            'fzht', 'fzst', 'fzkt', 'fzfs',
            
            # 汉仪字体
            '汉仪', 'hanyi', 'hy', '汉仪黑体', '汉仪宋体', '汉仪楷体',
            'hyht', 'hyst', 'hykt',
            
            # 蒙纳字体
            '蒙纳', 'monotype', 'mt',
            
            # 华康字体
            '华康', 'huakang', 'hk', '华康黑体', '华康宋体',
            'hkht', 'hkst',
            
            # 文鼎字体
            '文鼎', 'arphic', 'ar',
            
            # 造字工房
            '造字工房', 'makefont', 'mf',
            
            # 其他商业字体
            'helvetica', 'times new roman', 'arial', 'calibri', 'cambria',
            'comic sans', 'impact', 'lucida', 'palatino', 'tahoma',
            'trebuchet', 'verdana', 'garamond', 'minion', 'myriad',
            'optima', 'futura', 'avenir', 'din', 'gotham'
        }
        
        commercial_fonts.update(default_fonts)
        
        # 尝试从文件加载
        if rules_file.exists():
            try:
                content = read_file_safe(rules_file)
                for line in content.splitlines():
                    line = line.strip()
                    if line and not line.startswith('#'):
                        commercial_fonts.add(normalize_font_name(line))
            except Exception:
                pass
        
        return commercial_fonts
    
    def scan_directory(self, directory: Path) -> List[Dict]:
        """
        扫描目录中的字体使用问题
        
        Args:
            directory: 要扫描的目录
            
        Returns:
            问题列表
        """
        issues = []
        
        # 扫描代码文件中的字体引用
        for file_path in find_files(directory, {'web', 'javascript'}):
            issues.extend(self._scan_file_for_fonts(file_path))
        
        # 扫描字体文件
        for file_path in find_files(directory, {'font'}):
            issues.extend(self._scan_font_file(file_path))
        
        return issues
    
    def _scan_file_for_fonts(self, file_path: Path) -> List[Dict]:
        """
        扫描单个文件中的字体使用
        
        Args:
            file_path: 文件路径
            
        Returns:
            问题列表
        """
        issues = []
        content = read_file_safe(file_path)
        
        if not content:
            return issues
        
        # 检查 @font-face 声明
        font_face_issues = self._check_font_face(file_path, content)
        issues.extend(font_face_issues)
        
        # 检查 font-family 属性
        font_family_issues = self._check_font_family(file_path, content)
        issues.extend(font_family_issues)
        
        # 检查字体文件引用
        font_file_issues = self._check_font_file_references(file_path, content)
        issues.extend(font_file_issues)
        
        return issues
    
    def _check_font_face(self, file_path: Path, content: str) -> List[Dict]:
        """检查 @font-face 声明"""
        issues = []
        
        # 匹配 @font-face 规则
        font_face_pattern = r'@font-face\s*\{[^}]+\}'
        font_face_matches = re.findall(font_face_pattern, content, re.IGNORECASE | re.DOTALL)
        
        for match in font_face_matches:
            # 提取字体名称
            font_name_pattern = r'font-family\s*:\s*["\']?([^"\';\}]+)["\']?'
            name_matches = re.findall(font_name_pattern, match, re.IGNORECASE)
            
            for font_name in name_matches:
                normalized_name = normalize_font_name(font_name)
                if self._is_commercial_font(normalized_name):
                    issues.append(create_issue(
                        file_path=file_path,
                        issue_type='商业字体使用',
                        description=f'检测到商业字体 @font-face 声明: {font_name}',
                        risk_level='high',
                        details=f'在 @font-face 规则中使用了可能需要授权的商业字体: {font_name}'
                    ))
        
        return issues
    
    def _check_font_family(self, file_path: Path, content: str) -> List[Dict]:
        """检查 font-family 属性"""
        issues = []
        
        # 匹配 font-family 属性
        font_family_patterns = [
            r'font-family\s*:\s*([^;}\n]+)',  # CSS
            r'fontFamily\s*:\s*["\']([^"\']+)["\']',  # JS/React
            r'font-family\s*=\s*["\']([^"\']+)["\']'  # HTML
        ]
        
        for pattern in font_family_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            
            for match in matches:
                # 分割字体列表
                fonts = [f.strip().strip('\'"') for f in match.split(',')]
                
                for font in fonts:
                    normalized_font = normalize_font_name(font)
                    if self._is_commercial_font(normalized_font):
                        issues.append(create_issue(
                            file_path=file_path,
                            issue_type='商业字体使用',
                            description=f'检测到商业字体引用: {font}',
                            risk_level='medium',
                            details=f'在 font-family 属性中使用了可能需要授权的商业字体: {font}'
                        ))
        
        return issues
    
    def _check_font_file_references(self, file_path: Path, content: str) -> List[Dict]:
        """检查字体文件引用"""
        issues = []
        
        # 匹配字体文件URL
        font_url_patterns = [
            r'url\(["\']?([^"\')\s]*\.(?:ttf|otf|woff2?|eot))["\']?\)',  # CSS url()
            r'src\s*=\s*["\']([^"\']*\.(?:ttf|otf|woff2?|eot))["\']',    # HTML src
            r'["\']([^"\']*\.(?:ttf|otf|woff2?|eot))["\']'               # 一般引用
        ]
        
        for pattern in font_url_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            
            for font_url in matches:
                font_filename = Path(font_url).name.lower()
                if self._is_commercial_font_file(font_filename):
                    issues.append(create_issue(
                        file_path=file_path,
                        issue_type='商业字体文件引用',
                        description=f'检测到商业字体文件引用: {font_filename}',
                        risk_level='high',
                        details=f'引用了可能需要授权的商业字体文件: {font_url}'
                    ))
        
        return issues
    
    def _scan_font_file(self, file_path: Path) -> List[Dict]:
        """
        扫描字体文件
        
        Args:
            file_path: 字体文件路径
            
        Returns:
            问题列表
        """
        issues = []
        filename = file_path.name.lower()
        
        if self._is_commercial_font_file(filename):
            issues.append(create_issue(
                file_path=file_path,
                issue_type='商业字体文件',
                description=f'检测到商业字体文件: {file_path.name}',
                risk_level='high',
                details=f'项目中包含可能需要授权的商业字体文件: {file_path.name}'
            ))
        
        return issues
    
    def _is_commercial_font(self, font_name: str) -> bool:
        """
        判断是否为商业字体

        Args:
            font_name: 字体名称

        Returns:
            是否为商业字体
        """
        normalized_name = normalize_font_name(font_name)

        # 排除通用字体族名称
        generic_families = {'serif', 'sans-serif', 'monospace', 'cursive', 'fantasy', 'system-ui'}
        if normalized_name in generic_families:
            return False

        # 排除常见的系统字体
        system_fonts = {
            'arial', 'helvetica', 'times', 'courier', 'georgia', 'verdana',
            'tahoma', 'trebuchet ms', 'comic sans ms', 'impact', 'lucida console',
            'microsoft yahei', 'simsun', 'simhei', 'kaiti', 'fangsong'
        }
        if normalized_name in system_fonts:
            return False

        # 检查是否在商业字体列表中
        for commercial_font in self.commercial_fonts:
            if commercial_font in normalized_name or normalized_name in commercial_font:
                return True

        return False
    
    def _is_commercial_font_file(self, filename: str) -> bool:
        """
        判断字体文件是否为商业字体
        
        Args:
            filename: 字体文件名
            
        Returns:
            是否为商业字体文件
        """
        filename_lower = filename.lower()
        
        # 检查文件名中是否包含商业字体标识
        commercial_indicators = [
            'fz', 'fangzheng', '方正',
            'hy', 'hanyi', '汉仪',
            'hk', 'huakang', '华康',
            'mt', 'monotype', '蒙纳',
            'ar', 'arphic', '文鼎',
            'mf', 'makefont', '造字工房'
        ]
        
        for indicator in commercial_indicators:
            if indicator in filename_lower:
                return True
        
        return False
